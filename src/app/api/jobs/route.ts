import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

// Mock job data for development/testing
const MOCK_JOBS = [
  {
    id: "job-123",
    orgName: "ethereum",
    repoName: "solidity-examples",
    ref: "main",
    fuzzer: "ECHIDNA",
    directory: "contracts",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/abc123",
    corpusUrl: "https://example.com/corpus.tar.gz",
    coverageUrl: "https://example.com/coverage/index.html",
    logsUrl: "https://example.com/logs.txt",
    status: "SUCCESS",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T11:15:00Z",
    fuzzerArgs: {
      testLimit: 50000,
      timeout: 3600,
      corpusDir: "./corpus",
    },
    label: "Token Contract Security Test",
    metadata: {
      commit: "abc123def456",
      method: "manual",
      startedBy: "<EMAIL>",
    },
    testsDuration: "45m 32s",
    testsCoverage: 87.5,
    testsFailed: 3,
    testsPassed: 47,
    numberOfTests: 50,
    brokenProperties: [
      {
        brokenProperty: "echidna_balance_never_exceeds_supply",
        traces:
          "mint(******************************************, 1000000000000000000000000000)\ntransfer(******************************************, ******************************************, 500000000000000000000000000)",
      },
      {
        brokenProperty: "echidna_transfer_preserves_total",
        traces:
          "mint(******************************************, 1000000000000000000000)\ntransfer(******************************************, ******************************************, 2000000000000000000000)",
      },
    ],
    progress: 100,
    eta: null,
  },
  {
    id: "job-456",
    orgName: "openzeppelin",
    repoName: "openzeppelin-contracts",
    ref: "v4.9.0",
    fuzzer: "MEDUSA",
    directory: "contracts/token/ERC20",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/def456",
    corpusUrl: "https://example.com/corpus2.tar.gz",
    coverageUrl: "https://example.com/coverage2/index.html",
    logsUrl: "https://example.com/logs2.txt",
    status: "RUNNING",
    createdAt: "2024-01-15T12:00:00Z",
    updatedAt: "2024-01-15T12:30:00Z",
    fuzzerArgs: {
      timeout: 7200,
      workers: 4,
      corpusDir: "./corpus",
    },
    label: "ERC20 Implementation Fuzzing",
    metadata: {
      commit: "def456ghi789",
      method: "webhook",
      startedBy: "github-actions",
    },
    testsDuration: null,
    testsCoverage: 0,
    testsFailed: 0,
    testsPassed: 0,
    numberOfTests: 0,
    brokenProperties: [],
    progress: 65,
    eta: "25m 30s",
  },
];

// Get all jobs running for the organization that the user belongs to
export async function GET(req: NextRequest) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  // Return mock data for development/testing
  console.log("🎭 MOCK: Returning mock jobs data");

  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  return NextResponse.json({
    data: MOCK_JOBS,
    message: "Jobs retrieved successfully",
  });

  // Original implementation (commented out for mock)
  /*
  // It's guaranteed to be there due to the check above
  const token: JWT = await getToken({ req, secret });

  let foundData;
  try {
    foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/jobs/`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
  */
}
