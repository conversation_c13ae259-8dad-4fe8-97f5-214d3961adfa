import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { mockLogsByFuzzer } from "@/app/mocks";

export async function POST(req: NextRequest) {
  let localLogsUrl: string;
  try {
    const body = await req.json();
    const { logsUrl } = body;
    localLogsUrl = logsUrl;
  } catch (e) {
    return NextResponse.json(
      { data: {}, message: "Invalid request body" },
      { status: 500 }
    );
  }

  // Try to fetch logs from the provided URL, fallback to mock data if it fails
  try {
    const fetchLogs = await axios({
      method: "GET",
      url: localLogsUrl,
      timeout: 5000, // 5 second timeout
    });

    if (fetchLogs.status === 200) {
      return NextResponse.json(fetchLogs.data);
    }
  } catch (error) {
    console.log(
      "Failed to fetch logs from URL, using mock data:",
      error.message
    );
  }

  // Fallback to mock data - determine fuzzer type from URL or use default
  let mockLogs: string;

  // Try to determine fuzzer type from the logs URL pattern or job context
  if (localLogsUrl?.includes("medusa") || localLogsUrl?.includes("MEDUSA")) {
    mockLogs = mockLogsByFuzzer.MEDUSA;
  } else if (
    localLogsUrl?.includes("foundry") ||
    localLogsUrl?.includes("FOUNDRY")
  ) {
    mockLogs = mockLogsByFuzzer.FOUNDRY;
  } else if (
    localLogsUrl?.includes("halmos") ||
    localLogsUrl?.includes("HALMOS")
  ) {
    mockLogs = mockLogsByFuzzer.HALMOS;
  } else if (
    localLogsUrl?.includes("kontrol") ||
    localLogsUrl?.includes("KONTROL")
  ) {
    mockLogs = mockLogsByFuzzer.KONTROL;
  } else {
    mockLogs = mockLogsByFuzzer.ECHIDNA; // Default fallback
  }

  return NextResponse.json(mockLogs);
}
