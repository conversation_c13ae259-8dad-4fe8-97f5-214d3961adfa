"use client";
import { useEffect, useMemo, useState } from "react";
import { FaSortUp, FaSortDown } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppSelect } from "@/app/components/app-select";
import { AppSpinner } from "@/app/components/app-spinner";
import { Body1, Body3, Heading } from "@/app/components/app-typography";

import type { Job } from "@/app/services/jobs.hooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import type { BrokenPropShow } from "@/app/types/types";
import { searchObject } from "@/lib/utils";
import { formatDateString } from "@/utils/format";
import axios from "axios";
import { mockJobs } from "@/app/mocks";

// Component for job status and broken properties
const JobStatusSection = ({
  job,
  index,
  showBrokenPropDropdown,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div>
    {job.testsFailed != null && job.testsCoverage != null ? (
      <div className="mb-1 flex w-full gap-6">
        <Body3 color="primary" className="flex items-center gap-2">
          <span>{job.testsFailed == 0 ? "🟢" : "🔴"}</span>
          Failed: {job.testsFailed} - Coverage: {job.testsCoverage} - Status:{" "}
          {job.status}
          {job.progress && ` - Progress: ${job.progress}%`}
          {job.eta &&
            job?.progress < 100 &&
            ` - ETA: ${job.eta !== "0m" ? job.eta : "<1m"}`}
        </Body3>
      </div>
    ) : job.status === "ERROR" ? (
      <div className="mb-1">
        <Body3 color="primary">Status: {job.status}</Body3>
      </div>
    ) : null}

    {showBrokenPropDropdown.length > 0 &&
      job.brokenProperties &&
      job.brokenProperties.length > 0 && (
        <div>
          <AppButton
            variant="secondary"
            size="xs"
            className="mt-2"
            onClick={() => onToggleBrokenProps(index)}
          >
            <Body3>
              {showBrokenPropDropdown.find((el) => el.id === index)?.show
                ? "Hide"
                : "Show"}{" "}
              broken Props
            </Body3>
          </AppButton>
          {showBrokenPropDropdown.find((el) => el.id === index)?.show && (
            <div className="mt-2">
              <AppSelect
                options={job.brokenProperties.map((prop) => ({
                  value: prop.brokenProperty,
                  label: prop.brokenProperty,
                }))}
                placeholder="Select broken property"
                containerClassName="w-auto min-w-[200px]"
              />
            </div>
          )}
        </div>
      )}
  </div>
);

// Component for individual job card
const JobCard = ({
  job,
  index,
  isEditing,
  existingLabel,
  showBrokenPropDropdown,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  isEditing: boolean;
  existingLabel: string;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div className="flex flex-col gap-4 p-6 bg-back-neutral-tertiary rounded-lg border border-stroke-neutral-decorative shadow-sm hover:shadow-md transition-shadow duration-200">
    <div className="flex items-start justify-between gap-4">
      <div className="flex-1 min-w-0">
        <div className="mb-2 flex flex-wrap items-center gap-2">
          <EditableJobLabel
            job={job}
            isEditing={isEditing}
            existingLabel={existingLabel}
            onToggleEdit={onToggleEdit}
            onLabelChange={onLabelChange}
            onKeyPress={onKeyPress}
          />
          <Body1 color="primary">
            - {job.fuzzer} - {job.repoName} {job.ref} - Created{" "}
            {formatDateString(job.createdAt)} - Updated{" "}
            {formatDateString(job.updatedAt)}
          </Body1>
        </div>

        <JobStatusSection
          job={job}
          index={index}
          showBrokenPropDropdown={showBrokenPropDropdown}
          onToggleBrokenProps={onToggleBrokenProps}
        />
      </div>

      <div className="flex flex-col gap-2 shrink-0">
        <AppButton
          variant="outline"
          size="sm"
          onClick={() => window.open(`/dashboard/jobs/${job.id}`, "_self")}
        >
          View Details
        </AppButton>
        {(job.status === "SUCCESS" ||
          job.status === "ERROR" ||
          job.status === "STOPPED") && (
          <AppButton
            variant="outline"
            size="sm"
            onClick={() =>
              window.open(`/dashboard/jobs/${job.id}/report`, "_self")
            }
          >
            View Report
          </AppButton>
        )}
      </div>
    </div>
  </div>
);

// Component for sort toggle button
const SortToggle = ({
  sortDirection,
  onToggle,
}: {
  sortDirection: "asc" | "desc";
  onToggle: () => void;
}) => (
  <AppButton
    variant="outline"
    size="sm"
    onClick={onToggle}
    rightIcon={sortDirection === "asc" ? <FaSortUp /> : <FaSortDown />}
    className="min-w-fit"
  >
    <Body3 color="secondary">
      {sortDirection === "asc" ? "Oldest first" : "Newest first"}
    </Body3>
  </AppButton>
);

const FilterToggle = ({
  filterNoBroken,
  onToggle,
}: {
  filterNoBroken: boolean;
  onToggle: () => void;
}) => (
  <AppButton
    variant={filterNoBroken ? "secondary" : "outline"}
    size="sm"
    onClick={onToggle}
    className="min-w-fit"
  >
    <Body3 color={filterNoBroken ? "primary" : "secondary"}>
      {filterNoBroken ? "With broken props" : "All jobs"}
    </Body3>
  </AppButton>
);

// Component for sort by selector
const SortBySelector = ({
  sortBy,
  onSortByChange,
}: {
  sortBy: string;
  onSortByChange: (value: string) => void;
}) => {
  const sortOptions = [
    { value: "createdAt", label: "Created Date" },
    { value: "updatedAt", label: "Updated Date" },
    { value: "status", label: "Status" },
    { value: "fuzzer", label: "Fuzzer" },
  ];

  return (
    <div className="flex flex-col gap-1">
      <Body3 color="secondary" className="text-xs">
        Sort by
      </Body3>
      <AppSelect
        value={sortBy}
        onChange={(e) => onSortByChange(e.target.value)}
        options={sortOptions}
        containerClassName="w-[260px]"
      />
    </div>
  );
};

// Component for editable job label
const EditableJobLabel = ({
  job,
  isEditing,
  existingLabel,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
}: {
  job: Job;
  isEditing: boolean;
  existingLabel: string;
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}) => (
  <div
    onDoubleClick={onToggleEdit}
    className="cursor-pointer hover:bg-back-neutral-secondary rounded px-2 py-1 transition-colors"
  >
    {isEditing ? (
      <div>
        <input
          value={existingLabel}
          onChange={onLabelChange}
          onKeyDown={onKeyPress}
          className="min-w-[120px] h-10 rounded-md px-4 w-full outline-none transition-all duration-200 font-bold text-base bg-transparent border border-stroke-neutral-decorative text-fore-neutral-primary hover:border-fore-neutral-secondary focus:border-accent-primary placeholder:text-fore-neutral-quaternary"
          placeholder="Job label"
        />
      </div>
    ) : (
      <Body1 color="primary">{job.label || job.id.slice(0, 4)}</Body1>
    )}
  </div>
);

export const AllJobs = () => {
  const [isEditing, setIsEditing] = useState({});
  const [existingLabels, setExistingLabels] = useState({});
  // Use mock data for development
  const { data: apiData, isLoading, refetch, isRefetching } = useGetJobs();
  const data = apiData || mockJobs;

  const [showBrokenPropDropdown, setShowBrokenPropDropdown] = useState<
    BrokenPropShow[]
  >([]);

  const buttonDisabled = isLoading || isRefetching;

  const [sortBy, setSortBy] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  function toggleSortDirection() {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  }

  const [filterNoBroken, setFilterNoBroken] = useState(false);
  function toggleFilterBroken() {
    setFilterNoBroken(!filterNoBroken);
  }

  const [query, setQuery] = useState("");

  const sortedJobs = useMemo(() => {
    return (
      data
        ?.sort((jobA, jobB) => {
          const a = new Date(jobA[sortBy]).getTime();
          const b = new Date(jobB[sortBy]).getTime();
          return sortDirection === "asc" ? a - b : b - a;
        })
        // Keywords
        .filter((job) => {
          if (!query) return true;

          const queryWords = query.toLowerCase().split(/\s+/);
          return queryWords.every((word) => searchObject(job, word));
        })
        // Filter no broken props
        .filter((job) => (filterNoBroken ? job.testsFailed > 0 : true))
    );
  }, [sortBy, data, query, filterNoBroken, sortDirection]);

  useEffect(() => {
    if (sortedJobs && sortedJobs.length > 0) {
      setShowBrokenPropDropdown(
        sortedJobs.map((_, index) => ({
          id: index,
          show: false,
        }))
      );
    }
  }, [sortedJobs]);

  const showBrokenPropHandler = (index: number) => {
    setShowBrokenPropDropdown((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  useEffect(() => {
    if (!data) return;

    const newIsEditing = {};
    const newExistingLabels = {};

    data.forEach((job) => {
      newIsEditing[job.id] = false;
      newExistingLabels[job.id] = job.label ? job.label : "";
    });

    setIsEditing(newIsEditing);
    setExistingLabels(newExistingLabels);
  }, [data]);

  const updateJob = async (id: string) => {
    const res = await axios({
      method: "POST",
      url: `/api/jobs/updateLabel`,
      data: {
        newLabel: existingLabels[id],
        jobId: id,
      },
    });
    if (res.data.data === "success") {
      refetch();
    }
  };

  const newLabelHandling = (
    id: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExistingLabels({
      ...existingLabels,
      [id]: e.target.value,
    });
  };

  const handleKeyPress = (
    id: string,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter") {
      updateJob(id);
    }
  };

  return (
    <div className="mb-5 mt-12">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <Heading color="primary">All Jobs</Heading>
          <AppButton
            onClick={refetch}
            disabled={buttonDisabled}
            variant="outline"
            size="sm"
          >
            {buttonDisabled ? <AppSpinner /> : "Refresh"}
          </AppButton>
        </div>

        <div className="flex flex-wrap items-end gap-4 p-4 bg-back-neutral-secondary rounded-lg border border-stroke-neutral-decorative">
          <div className="flex flex-col gap-1 min-w-[300px]">
            <Body3 color="secondary" className="text-xs">
              Search
            </Body3>
            <AppInput
              type="text"
              placeholder="Search names, properties, fuzzer, coverage…"
              value={query}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setQuery(e.target.value)
              }
              className="w-full max-w-[300px]"
            />
          </div>

          <SortBySelector sortBy={sortBy} onSortByChange={setSortBy} />

          <div className="flex items-center gap-2">
            <SortToggle
              sortDirection={sortDirection}
              onToggle={toggleSortDirection}
            />
            <FilterToggle
              filterNoBroken={filterNoBroken}
              onToggle={toggleFilterBroken}
            />
          </div>

          <div className="ml-auto">
            <Body3 color="secondary">
              {sortedJobs?.length || 0} job{sortedJobs?.length !== 1 ? "s" : ""}{" "}
              found
            </Body3>
          </div>
        </div>
      </div>

      {sortedJobs?.length > 0 && (
        <div className="flex flex-col gap-5">
          {sortedJobs.map((job, index) => (
            <JobCard
              key={job.id}
              job={job}
              index={index}
              isEditing={isEditing[job.id]}
              existingLabel={existingLabels[job.id]}
              showBrokenPropDropdown={showBrokenPropDropdown}
              onToggleEdit={() =>
                setIsEditing((prevState) => ({
                  ...prevState,
                  [job.id]: !prevState[job.id],
                }))
              }
              onLabelChange={(event) => newLabelHandling(job.id, event)}
              onKeyPress={(event) => handleKeyPress(job.id, event)}
              onToggleBrokenProps={showBrokenPropHandler}
            />
          ))}
        </div>
      )}
    </div>
  );
};
