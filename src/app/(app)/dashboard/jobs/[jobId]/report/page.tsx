"use client";
import axios from "axios";
import { use, useEffect, useState } from "react";

import { downloadFile } from "@/app/(app)/tools/mdReportHelper";
import { AppButton } from "@/app/components/app-button";
import { AppHeader } from "@/app/(app)/components/app-header";
import { H2 } from "@/app/components/app-typography";
import JobReport from "@/app/components/JobReport/JobReport";
import { useGetJobById } from "@/app/services/jobs.hooks";
import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import { mockJobStatsByFuzzer, mockJobStats } from "@/app/mocks";
import Link from "next/link";

export default function ShareReport({
  params,
}: {
  params: Promise<{ jobId: string }>;
}) {
  const { jobId } = use(params);
  const [md, setMd] = useState("");
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { data: jobData } = useGetJobById(jobId);

  useEffect(() => {
    async function getLogs() {
      if (!jobData) return;

      try {
        const jobLogsRaw = await axios({
          method: "POST",
          url: `/api/fetchLogs`,
          data: {
            logsUrl: jobData?.logsUrl,
          },
        });
        const data = processLogs(jobLogsRaw.data, jobData?.fuzzer as Fuzzer);
        const jobStats = data;
        setJobStats(jobStats);

        const md = generateJobMD(
          jobData?.fuzzer as Fuzzer,
          jobLogsRaw.data,
          jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
        );
        setMd(md);
        setIsLoading(false);
      } catch (e) {
        console.log("Failed to fetch logs, using mock job stats:", e);
        // Use mock job stats as fallback
        const fuzzerType = (jobData?.fuzzer as string)?.toUpperCase();
        const mockStats = mockJobStatsByFuzzer[fuzzerType] || mockJobStats;
        setJobStats(mockStats);

        // Generate mock markdown report
        const mockMd = generateJobMD(
          jobData?.fuzzer as Fuzzer,
          "Mock log data - API unavailable",
          jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
        );
        setMd(mockMd);
        setIsLoading(false);
      }
    }
    getLogs();
  }, [jobData]);
  console.log("jobStats", jobStats);

  return (
    <>
      <AppHeader />
      <div className="min-h-[calc(100vh-73px)] bg-back-neutral-secondary dark:bg-back-neutral-primary">
        {isLoading === false ? (
          <div className="mx-auto max-w-7xl px-6 py-12">
            <H2 className="mb-6 text-accent-primary">
              Report for {jobData?.orgName}/{jobData?.repoName}/{jobData?.ref}
            </H2>
            <div className="mb-8 flex flex-row justify-between">
              {md ? (
                <AppButton onClick={() => downloadFile(md, jobData.repoName)}>
                  Download report
                </AppButton>
              ) : null}
              <Link href={`/dashboard/jobs/`}>
                <AppButton variant="secondary">Back to all runs</AppButton>
              </Link>
            </div>
            {md && jobData && jobStats ? (
              <JobReport
                fuzzer={jobData.fuzzer}
                jobStats={jobStats}
                showBrokenProp={true}
              />
            ) : null}
          </div>
        ) : (
          <div className="flex min-h-[calc(100vh-73px)] items-center justify-center">
            <H2 className="text-fore-neutral-primary">Loading...</H2>
          </div>
        )}
      </div>
    </>
  );
}
