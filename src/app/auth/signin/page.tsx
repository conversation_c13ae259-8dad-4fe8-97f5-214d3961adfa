"use client";

import { AppButton } from "@/app/components/app-button";
import { H1, Title3Strong } from "@/app/components/app-typography";
import {
  GradientWrapper,
  LPGradientBackground,
} from "@/app/components/gradient-wrapper";
import { createGradientTextStyle } from "@/app/(promo)/lp-components/gradient-text-utils";
import { getProviders, signIn } from "next-auth/react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { FaGithub } from "react-icons/fa";

interface Provider {
  id: string;
  name: string;
}

export default function SignIn() {
  const [providers, setProviders] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders();
      setProviders(res);
      setIsLoading(false);
    };

    fetchProviders();
  }, []);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <LPGradientBackground />
      {isLoading ? (
        <div className="flex h-screen items-center justify-center">
          <div className="text-white">Loading ...</div>
        </div>
      ) : (
        <GradientWrapper className="flex min-h-screen items-center justify-center py-8">
          <div className="w-full max-w-[720px] rounded-[20px] border border-stroke-neutral-decorative bg-back-neutral-secondary px-[48px] py-[40px] shadow-2xl backdrop-blur-sm">
            <div className="mb-10 flex flex-col items-center gap-1">
              <H1
                className="text-center"
                style={createGradientTextStyle("primary")}
              >
                Welcome to Recon
              </H1>
            </div>

            <div className="mb-10 flex flex-col gap-3">
              <Title3Strong color="secondary">Log in</Title3Strong>
              {providers &&
                Object.values(providers).map((provider: Provider) => (
                  <AppButton
                    key={provider.name}
                    variant="secondary"
                    size="lg"
                    fullWidth
                    className="flex items-center justify-center gap-[7px]"
                    onClick={() =>
                      signIn(provider.id, { callbackUrl: "/dashboard" })
                    }
                  >
                    <FaGithub size={24} />
                    <span>Sign in with Github</span>
                  </AppButton>
                ))}
            </div>
            <div className="mb-10 flex flex-col gap-3">
              <Title3Strong color="secondary">
                Try with Recon with no login
              </Title3Strong>
              <Link href="/tools/sandbox">
                <AppButton variant="outline" size="lg" fullWidth>
                  Scaffold invariants Sandbox
                </AppButton>
              </Link>
            </div>

            <div className="mb-10 flex flex-col gap-3">
              <Title3Strong color="secondary">Log Parsers</Title3Strong>
              <Link href="/tools/medusa">
                <AppButton variant="outline" size="lg" fullWidth>
                  Medusa Log to Foundry
                </AppButton>
              </Link>
              <Link href="/tools/echidna">
                <AppButton variant="outline" size="lg" fullWidth>
                  Echidna Log to Foundry
                </AppButton>
              </Link>
              <Link href="/tools/halmos">
                <AppButton variant="outline" size="lg" fullWidth>
                  Halmos Log to Foundry
                </AppButton>
              </Link>
            </div>

            <div className="flex flex-col gap-3">
              <Title3Strong color="secondary">Tools</Title3Strong>
              <Link href="/tools/bytecode-compare">
                <AppButton variant="outline" size="lg" fullWidth>
                  Bytecode compare
                </AppButton>
              </Link>
              <Link href="/tools/bytecode-to-interface">
                <AppButton variant="outline" size="lg" fullWidth>
                  Bytecode to interface
                </AppButton>
              </Link>
            </div>
          </div>
        </GradientWrapper>
      )}
    </div>
  );
}
