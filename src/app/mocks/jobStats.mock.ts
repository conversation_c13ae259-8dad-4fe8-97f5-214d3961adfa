import type { FuzzingResults } from "@recon-fuzz/log-parser";

// Mock job stats for ECHIDNA fuzzer
export const mockEchidnaJobStats: FuzzingResults = {
  duration: "2847",
  coverage: 92.3,
  failed: 2,
  passed: 6,
  numberOfTests: 8,
  results: [
    "echidna_test_deposit_withdraw: passed! 🎉",
    "echidna_test_balance_consistency: passed! 🎉", 
    "echidna_test_total_supply_invariant: passed! 🎉",
    "echidna_test_no_reentrancy: FAILED!💥",
    "echidna_test_access_control: passed! 🎉",
    "echidna_test_fee_calculation: FAILED!💥",
    "echidna_test_emergency_pause: passed! 🎉",
    "echidna_test_ownership_transfer: passed! 🎉"
  ],
  traces: [
    "deposit(1000000000000000000) -> withdraw(500000000000000000) -> emergencyWithdraw()",
    "setFeeRate(10000) -> deposit(1000000000000000000) -> withdraw(1000000000000000000)"
  ],
  brokenProperties: [
    "echidna_test_no_reentrancy: Reentrancy vulnerability detected in emergencyWithdraw function",
    "echidna_test_fee_calculation: Fee calculation overflow when fee rate exceeds 100%"
  ]
};

// Mock job stats for MEDUSA fuzzer
export const mockMedusaJobStats: FuzzingResults = {
  duration: "1823",
  coverage: 87.5,
  failed: 1,
  passed: 7,
  numberOfTests: 8,
  results: [
    "test_token_transfer: PASSED ✓",
    "test_approval_mechanism: PASSED ✓",
    "test_burn_functionality: PASSED ✓",
    "test_mint_authorization: FAILED ✗",
    "test_pause_mechanism: PASSED ✓",
    "test_ownership_controls: PASSED ✓",
    "test_metadata_integrity: PASSED ✓",
    "test_batch_operations: PASSED ✓"
  ],
  traces: [
    "mint(0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b9, 1000000) -> transfer(0x1234567890123456789012345678901234567890, 500000)"
  ],
  brokenProperties: [
    "test_mint_authorization: Unauthorized minting detected - non-owner can mint tokens"
  ]
};

// Mock job stats for FOUNDRY fuzzer
export const mockFoundryJobStats: FuzzingResults = {
  duration: "456",
  coverage: 94.7,
  failed: 0,
  passed: 12,
  numberOfTests: 12,
  results: [
    "test_deposit_withdraw_fuzz: PASSED ✓ (runs: 256, μ: 89234, ~: 89156)",
    "test_fee_calculation_fuzz: PASSED ✓ (runs: 256, μ: 45123, ~: 45089)",
    "test_access_control_fuzz: PASSED ✓ (runs: 256, μ: 67890, ~: 67845)",
    "test_emergency_functions_fuzz: PASSED ✓ (runs: 256, μ: 123456, ~: 123401)",
    "test_token_transfers_fuzz: PASSED ✓ (runs: 256, μ: 34567, ~: 34523)",
    "test_approval_edge_cases_fuzz: PASSED ✓ (runs: 256, μ: 78901, ~: 78856)",
    "test_burn_scenarios_fuzz: PASSED ✓ (runs: 256, μ: 56789, ~: 56734)",
    "test_mint_conditions_fuzz: PASSED ✓ (runs: 256, μ: 91234, ~: 91189)",
    "test_pause_unpause_fuzz: PASSED ✓ (runs: 256, μ: 23456, ~: 23412)",
    "test_ownership_transfer_fuzz: PASSED ✓ (runs: 256, μ: 65432, ~: 65387)",
    "test_metadata_updates_fuzz: PASSED ✓ (runs: 256, μ: 87654, ~: 87609)",
    "test_batch_operations_fuzz: PASSED ✓ (runs: 256, μ: 43210, ~: 43165)"
  ],
  traces: [],
  brokenProperties: []
};

// Mock job stats for HALMOS fuzzer
export const mockHalmosJobStats: FuzzingResults = {
  duration: "892",
  coverage: 89.2,
  failed: 2,
  passed: 4,
  numberOfTests: 6,
  results: [
    "Claim 1: deposit_invariant - PROVED ✓",
    "Claim 2: withdrawal_authorization - FAILED ✗",
    "Claim 3: balance_consistency - PROVED ✓", 
    "Claim 4: no_reward_before_staking - PROVED ✓",
    "Claim 5: withdrawal_authorization - FAILED ✗",
    "Claim 6: emergency_pause_effectiveness - PROVED ✓"
  ],
  traces: [
    "User A stakes 1000 tokens -> User B can withdraw User A's stake",
    "emergencyPause() called -> users can still withdraw funds"
  ],
  brokenProperties: [
    "withdrawal_authorization: User B can withdraw User A's stake without proper authorization",
    "emergency_pause_effectiveness: Emergency pause does not prevent all user operations"
  ]
};

// Mock job stats for KONTROL fuzzer
export const mockKontrolJobStats: FuzzingResults = {
  duration: "1456",
  coverage: 91.8,
  failed: 1,
  passed: 5,
  numberOfTests: 6,
  results: [
    "bridge_deposit_proof: PROVED ✓",
    "bridge_withdrawal_proof: PROVED ✓",
    "cross_chain_consistency: FAILED ✗",
    "fee_calculation_proof: PROVED ✓",
    "emergency_halt_proof: PROVED ✓",
    "validator_consensus_proof: PROVED ✓"
  ],
  traces: [
    "deposit(chainA, 1000) -> withdraw(chainB, 1000) -> balance_mismatch"
  ],
  brokenProperties: [
    "cross_chain_consistency: Balance inconsistency detected between chains during concurrent operations"
  ]
};

// Map of fuzzer types to their corresponding mock job stats
export const mockJobStatsByFuzzer = {
  ECHIDNA: mockEchidnaJobStats,
  MEDUSA: mockMedusaJobStats,
  FOUNDRY: mockFoundryJobStats,
  HALMOS: mockHalmosJobStats,
  KONTROL: mockKontrolJobStats,
};

// Default mock job stats (Echidna format)
export const mockJobStats = mockEchidnaJobStats;
